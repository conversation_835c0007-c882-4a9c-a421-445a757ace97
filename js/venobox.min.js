/*
 * VenoBox - jQuery Plugin
 * version: 1.8.0
 * @requires jQuery >= 1.7.0
 *
 * Examples at http://veno.es/venobox/
 * License: MIT License
 * License URI: https://github.com/nicolafranchini/VenoBox/blob/master/LICENSE
 * Copyright 2013-2017 <PERSON> - @nicolafranchini
 *
 */
!function(e){var o,t,a,s,i,n,c,r,d,l,v,u,b,p,m,f,h,g,k,x,y,w,C,_,E,M,P,T,B;e.fn.extend({venobox:function(Y){var O=this,U={arrowsColor:"#B6B6B6",autoplay:!1,bgcolor:"#fff",border:"0",closeBackground:"#161617",closeColor:"#d2d2d2",framewidth:"",frameheight:"",infinigall:!1,htmlClose:"&times;",htmlNext:"<span>Next</span>",htmlPrev:"<span>Prev</span>",numeratio:!1,numerationBackground:"#161617",numerationColor:"#d2d2d2",numerationPosition:"top",overlayClose:!0,overlayColor:"rgba(23,23,23,0.85)",spinner:"double-bounce",spinColor:"#d2d2d2",titleattr:"title",titleBackground:"#161617",titleColor:"#d2d2d2",titlePosition:"top",cb_pre_open:function(){return!0},cb_post_open:function(){},cb_pre_close:function(){return!0},cb_post_close:function(){},cb_post_resize:function(){},cb_after_nav:function(){},cb_init:function(){}},X=e.extend(U,Y);return X.cb_init(O),this.each(function(){function Y(){y=T.data("gall"),h=T.data("numeratio"),b=T.data("infinigall"),p=e('.vbox-item[data-gall="'+y+'"]'),w=p.eq(p.index(T)+1),C=p.eq(p.index(T)-1),p.length>1?(B=p.index(T)+1,a.html(B+" / "+p.length)):B=1,h===!0?a.show():a.hide(),""!==x?s.show():s.hide(),w.length||b===!0?(e(".vbox-next").css("display","block"),_=!0):(e(".vbox-next").css("display","none"),_=!1),p.index(T)>0||b===!0?(e(".vbox-prev").css("display","block"),E=!0):(e(".vbox-prev").css("display","none"),E=!1),(E===!0||_===!0)&&(r.on(TouchMouseEvent.DOWN,V),r.on(TouchMouseEvent.MOVE,z),r.on(TouchMouseEvent.UP,R))}function U(e){return e.length<1?!1:m?!1:(m=!0,g=e.data("overlay")||e.data("overlaycolor"),v=e.data("framewidth"),u=e.data("frameheight"),i=e.data("border"),t=e.data("bgcolor"),d=e.data("href")||e.attr("href"),o=e.data("autoplay"),x=e.attr(e.data("titleattr"))||"",e===C&&r.addClass("animated").addClass("swipe-right"),e===w&&r.addClass("animated").addClass("swipe-left"),void r.animate({opacity:0},500,function(){k.css("background",g),r.removeClass("animated").removeClass("swipe-left").removeClass("swipe-right").css({"margin-left":0,"margin-right":0}),"iframe"==e.data("vbtype")?W():"inline"==e.data("vbtype")?A():"ajax"==e.data("vbtype")?j():"video"==e.data("vbtype")||"vimeo"==e.data("vbtype")||"youtube"==e.data("vbtype")?$(o):(r.html('<img src="'+d+'">'),H()),T=e,Y(),m=!1,X.cb_after_nav(T,B,w,C)}))}function D(e){27===e.keyCode&&N(),37==e.keyCode&&E===!0&&U(C),39==e.keyCode&&_===!0&&U(w)}function N(){var o=X.cb_pre_close(T,B,w,C);return o===!1?!1:(e("body").off("keydown",D).removeClass("vbox-open"),T.focus(),void k.animate({opacity:0},500,function(){k.remove(),m=!1,X.cb_post_close()}))}function V(e){r.addClass("animated"),startY=e.pageY,F=e.pageX,startouch=!0}function z(e){if(startouch===!0){G=e.pageX,endY=e.pageY,diffX=G-F,diffY=endY-startY;var o=Math.abs(diffX),t=Math.abs(diffY);o>t&&100>=o&&(e.preventDefault(),r.css("margin-left",diffX))}}function R(e){if(startouch===!0){startouch=!1;var o=T,t=!1;J=G-F,0>J&&_===!0&&(o=w,t=!0),J>0&&E===!0&&(o=C,t=!0),Math.abs(J)>=K&&t===!0?U(o):r.css({"margin-left":0,"margin-right":0})}}function j(){e.ajax({url:d,cache:!1}).done(function(e){r.html('<div class="vbox-inline">'+e+"</div>"),H()}).fail(function(){r.html('<div class="vbox-inline"><p>Error retrieving contents, please retry</div>'),Q()})}function W(){r.html('<iframe class="venoframe" src="'+d+'"></iframe>'),Q()}function $(e){var o,t=q(d),a=e?"?rel=0&autoplay=1":"?rel=0",s=a+I(d);"vimeo"==t.type?o="https://player.vimeo.com/video/":"youtube"==t.type&&(o="https://www.youtube.com/embed/"),r.html('<iframe class="venoframe vbvid" webkitallowfullscreen mozallowfullscreen allowfullscreen frameborder="0" src="'+o+t.id+s+'"></iframe>'),Q()}function q(e){if(e.match(/(http:|https:|)\/\/(player.|www.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com))\/(video\/|embed\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/),RegExp.$3.indexOf("youtu")>-1)var o="youtube";else if(RegExp.$3.indexOf("vimeo")>-1)var o="vimeo";return{type:o,id:RegExp.$6}}function I(e){var o="",t=decodeURIComponent(e),a=t.split("?");if(void 0!==a[1]){var s,i,n=a[1].split("&");for(i=0;i<n.length;i++)s=n[i].split("="),o=o+"&"+s[0]+"="+s[1]}return encodeURI(o)}function A(){r.html('<div class="vbox-inline">'+e(d).html()+"</div>"),Q()}function H(){images=r.find("img"),images.length?images.each(function(){e(this).one("load",function(){Q()})}):Q()}function Q(){s.html(x),r.find(">:first-child").addClass("figlio").css({width:v,height:u,padding:i,background:t}),e("img.figlio").on("dragstart",function(e){e.preventDefault()}),S(),r.animate({opacity:"1"},"slow",function(){})}function S(){var o=r.outerHeight(),t=e(window).height();t>o+60?(f=(t-o)/2,r.css("margin-top",f),r.css("margin-bottom",f)):(f="30px",r.css("margin-top",f),r.css("margin-bottom",f)),X.cb_post_resize()}if(T=e(this),T.data("venobox"))return!0;O.VBclose=function(){N()},T.addClass("vbox-item"),T.data("framewidth",X.framewidth),T.data("frameheight",X.frameheight),T.data("border",X.border),T.data("bgcolor",X.bgcolor),T.data("numeratio",X.numeratio),T.data("infinigall",X.infinigall),T.data("overlaycolor",X.overlayColor),T.data("titleattr",X.titleattr),T.data("venobox",!0),T.on("click",function(b){b.preventDefault(),T=e(this);var p=X.cb_pre_open(T);if(p===!1)return!1;switch(O.VBnext=function(){U(w)},O.VBprev=function(){U(C)},g=T.data("overlay")||T.data("overlaycolor"),v=T.data("framewidth"),u=T.data("frameheight"),o=T.data("autoplay")||X.autoplay,i=T.data("border"),t=T.data("bgcolor"),_=!1,E=!1,m=!1,d=T.data("href")||T.attr("href"),l=T.data("css")||"",x=T.attr(T.data("titleattr"))||"",M='<div class="vbox-preloader">',X.spinner){case"rotating-plane":M+='<div class="sk-rotating-plane"></div>';break;case"double-bounce":M+='<div class="sk-double-bounce"><div class="sk-child sk-double-bounce1"></div><div class="sk-child sk-double-bounce2"></div></div>';break;case"wave":M+='<div class="sk-wave"><div class="sk-rect sk-rect1"></div><div class="sk-rect sk-rect2"></div><div class="sk-rect sk-rect3"></div><div class="sk-rect sk-rect4"></div><div class="sk-rect sk-rect5"></div></div>';break;case"wandering-cubes":M+='<div class="sk-wandering-cubes"><div class="sk-cube sk-cube1"></div><div class="sk-cube sk-cube2"></div></div>';break;case"spinner-pulse":M+='<div class="sk-spinner sk-spinner-pulse"></div>';break;case"three-bounce":M+='<div class="sk-three-bounce"><div class="sk-child sk-bounce1"></div><div class="sk-child sk-bounce2"></div><div class="sk-child sk-bounce3"></div></div>';break;case"cube-grid":M+='<div class="sk-cube-grid"><div class="sk-cube sk-cube1"></div><div class="sk-cube sk-cube2"></div><div class="sk-cube sk-cube3"></div><div class="sk-cube sk-cube4"></div><div class="sk-cube sk-cube5"></div><div class="sk-cube sk-cube6"></div><div class="sk-cube sk-cube7"></div><div class="sk-cube sk-cube8"></div><div class="sk-cube sk-cube9"></div></div>'}return M+="</div>",P='<a class="vbox-next">'+X.htmlNext+'</a><a class="vbox-prev">'+X.htmlPrev+"</a>",vbheader='<div class="vbox-title"></div><div class="vbox-num">0/0</div><div class="vbox-close">'+X.htmlClose+"</div>",n='<div class="vbox-overlay '+l+'" style="background:'+g+'">'+M+'<div class="vbox-container"><div class="vbox-content"></div></div>'+vbheader+P+"</div>",e("body").append('<div class="ghostoverlay"></div>').append(n).addClass("vbox-open"),e(".vbox-preloader .sk-child, .vbox-preloader .sk-rotating-plane, .vbox-preloader .sk-rect, .vbox-preloader .sk-cube, .vbox-preloader .sk-spinner-pulse").css("background-color",X.spinColor),k=e(".vbox-overlay"),c=e(".vbox-container"),r=e(".vbox-content"),a=e(".vbox-num"),s=e(".vbox-title"),s.css(X.titlePosition,"-1px"),s.css({color:X.titleColor,"background-color":X.titleBackground}),e(".vbox-close").css({color:X.closeColor,"background-color":X.closeBackground}),e(".vbox-num").css(X.numerationPosition,"-1px"),e(".vbox-num").css({color:X.numerationColor,"background-color":X.numerationBackground}),e(".vbox-next span, .vbox-prev span").css({"border-top-color":X.arrowsColor,"border-right-color":X.arrowsColor}),r.html(""),r.css("opacity","0"),Y(),k.animate({opacity:1},250,function(){"iframe"==T.data("vbtype")?W():"inline"==T.data("vbtype")?A():"ajax"==T.data("vbtype")?j():"video"==T.data("vbtype")||"vimeo"==T.data("vbtype")||"youtube"==T.data("vbtype")?$(o):(r.html('<img src="'+d+'">'),H()),X.cb_post_open(T,B,w,C)}),e("body").keydown(D),e(".vbox-prev").on("click",function(){U(C)}),e(".vbox-next").on("click",function(){U(w)}),!1});var Z=".vbox-overlay";X.overlayClose||(Z=".vbox-close"),e(document).on("click",Z,function(o){(e(o.target).is(".vbox-overlay")||e(o.target).is(".vbox-content")||e(o.target).is(".vbox-close")||e(o.target).is(".vbox-preloader"))&&N()});var F=null,G=null,J=0,K=50;startouch=!1,TouchMouseEvent={DOWN:"touchmousedown",UP:"touchmouseup",MOVE:"touchmousemove"};var L=function(o){var t;switch(o.type){case"mousedown":t=TouchMouseEvent.DOWN;break;case"mouseup":t=TouchMouseEvent.UP;break;case"mouseout":t=TouchMouseEvent.UP;break;case"mousemove":t=TouchMouseEvent.MOVE;break;default:return}var a=oe(t,o,o.pageX,o.pageY);e(o.target).trigger(a)},ee=function(o){var t;switch(o.type){case"touchstart":t=TouchMouseEvent.DOWN;break;case"touchend":t=TouchMouseEvent.UP;break;case"touchmove":t=TouchMouseEvent.MOVE;break;default:return}var a,s=o.originalEvent.touches[0];a=t==TouchMouseEvent.UP?oe(t,o,null,null):oe(t,o,s.pageX,s.pageY),e(o.target).trigger(a)},oe=function(o,t,a,s){return e.Event(o,{pageX:a,pageY:s,originalEvent:t})};"ontouchstart"in window?(e(document).on("touchstart",ee),e(document).on("touchmove",ee),e(document).on("touchend",ee)):(e(document).on("mousedown",L),e(document).on("mouseup",L),e(document).on("mouseout",L),e(document).on("mousemove",L)),e(window).resize(function(){e(".vbox-content").length&&setTimeout(S(),800)})})}})}(jQuery);