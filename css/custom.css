/*========================================================================

	Template Name: Multipurpose Bootstrap 5 Template
	Author: Rainbow Design

========================================================================*/

/*----------------------------------

	Table of contents
	
	01. Elements style import
	02. Common
	03. Header
	04. Slider
	05. About area
	06. Portfolio
	07. Footer
	08. Inner page header
	09. Services
	10. Sidebar services
	11. Sidebar about
	12. Sidebar download brouchure
	13. Single portfolio
	14. Login
	15. Error page 404
	16. Coming soon page
	17. Contact Us
	18. Privacy policy
	19. Gallery
	20. Partners
	21. Listing
	22. List view
	23. Detail page
	
----------------------------------*/

/* ===================================
	01. Elements style import
=================================== */

@import "elements/accordion.css";
@import "elements/blockquotes.css";
@import "elements/buttons.css";
@import "elements/dropcaps.css";
@import "elements/icon-box.css";
@import "elements/image-box.css";
@import "elements/call-to-action.css";
@import "elements/image-box.css";
@import "elements/blog.css";
@import "elements/testimonial.css";
@import "elements/counter.css";
@import "elements/pricing.css";
@import "elements/skill.css";
@import "elements/team.css";
@import "elements/tabs.css";
@import "elements/list-style.css";
@import "elements/video.css";


/* ===================================
	02. Common
=================================== */
/* Theme Font Use */

@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
/* font-family: 'Montserrat', sans-serif; */

::selection {
	background-color: #f44647;
	color: #fff;
}
::-moz-selection {
 background-color: #f44647;
 color: #fff;
}
img {
	max-width: 100%;
}
body {
	font-size: 12px;
	font-family: 'Montserrat', sans-serif;
	color: #c5c5c5;
	letter-spacing: 1px;
}
p {
	line-height: 24px;
	font-size: 14px;
	color: #6c757d;
	font-family: 'Montserrat', sans-serif;
}
h1, h2, h3, h4, h5, h6 {
	margin: 0px;
	color: #202020;
	padding: 0px;
	line-height: 1.2;
	letter-spacing: 1px;
	font-family: 'Montserrat', sans-serif;
}
ul, ol, li {
	margin: 0px;
	padding: 0px;
	list-style: none;
}
a {
	text-decoration: none;
	cursor: pointer;
}
a:hover {
	text-decoration: none;
}
.element-title {
	font-weight: 400;
	text-align: center;
	margin-bottom: 30px;
}
/* --- Top Scroll --- */
.scroll-top {
	display: none;
	position: fixed;
	bottom: 2px;
	text-align: center;
	right: 2%;
	width: 40px;
	height: 40px;
	line-height: 40px;
	font-size: 12px;
	color: #fff;
	background-color: #1de278;
	text-decoration: none;
	border-radius: 100%;
	z-index: 9999;
}
.scroll-top:hover {
	color: #fff !important;
	text-decoration: none;
	background-color: #000;
}
/* --- Loading --- */
#preloader {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 99999;
	width: 100%;
	text-align: center;
	height: 100%;
	vertical-align: middle;
	height: 100%;
	overflow: visible;
	background-color: #fff;
}
#preloader .spinner-grow {
	top: 50%;
	position: relative;
}
/* --- section title --- */
.section-title h6 {
	font-size: 12px;
	color: #6c757d;
	margin-bottom: 6px;
	text-transform: uppercase;
}
.section-title h2 {
	font-size: 36px;
	font-weight: 600;
}
/* ===================================
	03. Header
=================================== */
.header-upper {
	background-color: #f5f5f5;
	padding: 10px 0px;
}
/* header social */
.header-social li {
	margin-right: 30px !important;
}
.header-social li:last-child {
	margin-right: 0px !important;
}
.header-social li a {
	margin: 0px !important;
	text-align: center;
	display: inline-block;
	padding: 0px;
	color: #9f9f9f;
	font-size: 14px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.header-social li a:hover {
	color: #000;
}
.header-top-right ul.header-top-menu {
	position: relative;
}
.header-top-right ul.header-top-menu li a {
	color: #333;
}
.header-top-right ul.header-top-menu li {
	margin-right: 20px;
}
.header-top-right ul.header-top-menu li:last-child {
	margin-right: 0px;
}
.header-top-right ul.header-top-menu .dropdown ul.dropdown-menu {
	padding: 0px;
	border-radius: 6px;
}
.header-top-right ul.header-top-menu li:nth-child(1) .dropdown ul.dropdown-menu li {
	margin-right: 0px;
}
.header-top-right ul.header-top-menu li:nth-child(1) .dropdown ul.dropdown-menu li p {
	font-size: 12px;
	color: #fff;
	background-color: #333;
	text-align: center;
	text-transform: uppercase;
	padding: 8px 12px;
	font-weight: 600;
	-webkit-border-radius: 6px 6px 0 0;
	-moz-border-radius: 6px 6px 0 0;
	-ms-border-radius: 6px 6px 0 0;
	border-radius: 6px 6px 0 0;
	white-space: nowrap;
	margin-bottom: 0px;
}
.header-top-right ul.header-top-menu li:nth-child(1) .dropdown ul.dropdown-menu:before {
	right: 30px;
	content: "";
	position: absolute;
	z-index: 1000;
	top: -4px;
	width: 9px;
	height: 9px;
	-webkit-transform: rotate(-135deg);
	-ms-transform: rotate(-135deg);
	transform: rotate(-135deg);
	background-color: #333;
}
.header-top-right ul.header-top-menu li .dropdown ul.dropdown-menu li:nth-child(2) a {
	padding-top: 16px;
}
.header-top-right ul.header-top-menu li .dropdown ul.dropdown-menu li a {
	display: block;
	color: #666;
	font-size: 12px;
	padding: 0px 22px 16px 22px;
}
.header-top-right ul.header-top-menu li .dropdown ul.dropdown-menu li a:hover {
	color: #000
}
.header-top-right ul.header-top-menu li.signup a {
	padding: 8px 12px;
	background-color: #333;
	border-radius: 4px;
	color: #fff;
	font-weight: 600;
	font-size: 12px;
	display: inline-block;
}
.header-top-right ul.header-top-menu li.login a {
	font-weight: 600;
	font-size: 12px;
	text-decoration: underline;
}
.header-lover .navbar-brand {
	padding: 0px;
	margin: 0px;
}
.header-lover {
	padding: 10px 0px;
	position: relative;
	border-bottom: 1px solid rgba(0,0,0,0.1);
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.header-lover .navbar {
	padding: 0px 0px;
}
.header-lover .navbar .nav-item .nav-link {
	font-size: 12px;
	color: #666;
	font-weight: 500;
	position: relative;
	padding: 26px 14px;
	text-transform: uppercase;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.header-lover .navbar .nav-item .nav-link:hover, .header-lover .navbar .nav-item.active .nav-link {
	color: #000;
}
.header-lover .navbar-toggler span {
	background: #000;
}
.header-lover .dropdown-menu {
	margin: 0px;
	padding: 0px;
	border: 0px;
	border-radius: 8px;
	background-color: #333;
}
.header-lover .dropdown-menu:before {
	left: 30px;
	content: "";
	position: absolute;
	z-index: 9999;
	top: -4px;
	width: 9px;
	height: 9px;
	-webkit-transform: rotate(-135deg);
	-ms-transform: rotate(-135deg);
	transform: rotate(-135deg);
	background-color: #333;
}
.header-lover .dropdown-menu .dropdown-item {
	border-bottom: 1px solid rgba(255,255,255,0.1) !important;
	position: relative;
	font-size: 11px;
	padding: 12px 12px;
	color: #999;
	font-weight: 500;
	text-transform: uppercase;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.header-lover .dropdown-menu li:last-child .dropdown-item {
	border-bottom: 0px !important;
}
.header-lover .dropdown-menu .dropdown-item:hover {
	color: #fff;
	padding-left: 20px;
	background-color: transparent;
}
/* Navbar Toogle */
.navbar-toggler {
	border: none;
	padding: 10px 6px;
	background-color: #f5f5f5;
	outline: none !important;
}
.navbar-toggler span {
	display: block;
	width: 22px;
	height: 2px;
	border-radius: 1px;
	background: #333;
}
.navbar-toggler span + span {
	margin-top: 4px;
	width: 18px;
}
.navbar-toggler span + span + span {
	width: 10px;
}
.header-lover .navbar .navbar-nav {
	align-items: center;
}
.header-lover .navbar .nav-item.header-search .nav-link {
	background-color: #333;
	color: #fff;
	border-radius: 100%;
	width: 40px;
	height: 40px;
	line-height: 40px;
	padding: 0px;
	text-align: center;
}
/* ---- mega menu ---- */
.navbar .mega-menu {
	position: static;
}
.navbar .mega-dropdown-menu {
	width: 100%;
	left: 0;
	right: 0;
	/* height of nav-item */
	padding: 10px 20px 20px 20px;
	background-color: #fff;
	border: 1px solid rgba(0,0,0,0.05);
	-webkit-box-shadow: 5px 5px 10px 0 rgba(68,68,68,0.15);
	box-shadow: 5px 5px 10px 0 rgba(68,68,68,0.15);
}
.navbar .mega-dropdown-menu:before {
	display: none;
}
.mega-dropdown-menu-title {
	font-size: 18px;
	font-weight: 700;
	padding: 10px 0px 10px 0px;
	position: relative;
	border-bottom: 2px solid #1de278;
	text-transform: uppercase;
	margin-bottom: 20px;
	position: relative;
}
.navbar .mega-dropdown-menu .container {
	background: #fff;
	display: inline-block;
	padding: 0px;
}
.navbar .mega-dropdown-menu ul {
	border-right: 1px solid rgba(0,0,0,0.05);
}
.navbar .mega-dropdown-menu .row .col:last-child ul {
	border-right: 0px;
}
.navbar .mega-dropdown-menu ul li a {
	padding: 10px 10px !important;
	position: relative;
	text-transform: uppercase;
	font-size: 11px !important;
	border-radius: 0px !important;
}
.navbar .mega-dropdown-menu ul li a:hover {
	color: #fff !important;
	background-color: #333;
}
/* SearchModal */
#SearchModal form input {
	padding: 20px 16px;
	font-size: 12px;
	-webkit-box-shadow: inset 0px 0px 10px 0px rgba(196,196,196,1);
	-moz-box-shadow: inset 0px 0px 10px 0px rgba(196,196,196,1);
	box-shadow: inset 0px 0px 10px 0px rgba(196,196,196,1);
}
/* sticky */
.is-sticky {
	z-index: 9999;
}
.is-sticky .header-lover {
	z-index: 999 !important;
	background-color: #fff !important;
 box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
}
.is-sticky .header-lover .navbar .nav-item .nav-link {
	color: #333;
}
.is-sticky .nav-item.active .nav-link, .is-sticky .nav-pills .show > .nav-link {
	color: #ee4155 !important;
}
/* header 2 */
.header-2 .header-upper {
	background-color: #000;
}
.header-2 .header-social li a:hover {
	color: #fff;
}
.header-2 .header-top-right ul.header-top-menu li a {
	color: #fff;
}
/* header 3 */
.header-3 {
	position: absolute;
	z-index: 3;
	width: 100%;
}
.header-3 .header-upper {
	background-color: rgba(0,0,0,0.2);
}
.header-3 .header-lover {
	border-bottom: 1px solid rgba(255,255,255,0.2);
}
.header-3 .header-social li a {
	color: #ccc;
}
.header-3 .header-social li a:hover {
	color: #fff;
}
.header-3 .header-top-right ul.header-top-menu li a {
	color: #fff;
}
.header-3 .header-top-right ul.header-top-menu li.signup a {
	background-color: #1de278;
}
.header-3 .header-lover .navbar .nav-item .nav-link {
	color: #fff;
}
.header-3 .header-lover .navbar .nav-item .nav-link:hover, .header-3 .header-lover .navbar .nav-item.active .nav-link {
	color: #1de278;
}
.header-3 .navbar .mega-dropdown-menu ul li a {
	color: #666 !important;
}
.header-3 .navbar .mega-dropdown-menu ul li a:hover {
	color: #fff !important;
	background-color: #333;
}
/* header 4 */
.header-4 {
	position: absolute;
	z-index: 3;
	width: 100%;
}
.header-4 .header-upper {
	background-color: transparent;
	border-bottom: 1px solid rgba(255,255,255,0.2);
}
.header-4 .header-lover {
	border-bottom: 1px solid rgba(255,255,255,0.2);
}
.header-4 .header-social li a {
	color: #fff;
}
.header-4 .header-social li a:hover {
	color: #000;
}
.header-4 .header-top-right ul.header-top-menu li a {
	color: #fff;
}
.header-4 .header-top-right ul.header-top-menu li.signup a {
	background-color: #1de278;
}
.header-4 .header-lover .navbar .nav-item .nav-link {
	color: #fff;
}
.header-4 .header-lover .navbar .nav-item .nav-link:hover, .header-3 .header-lover .navbar .nav-item.active .nav-link {
	color: #1de278;
}
.header-4 .navbar .mega-dropdown-menu ul li a {
	color: #666 !important;
}
.header-4 .navbar .mega-dropdown-menu ul li a:hover {
	color: #fff !important;
	background-color: #333;
}
/* mydash */
.mydash .dropdown .dropdown-menu li a {
	display: block;
	color: #666;
	font-size: 12px;
	white-space: nowrap;
	padding: 10px 16px !important;
}
.mydash .dropdown .dropdown-menu li {
	margin-right: 0px;
}
.mydash .dropdown .dropdown-menu {
	padding: 5px 0px !important;
}
.mydash .dropdown .dropdown-menu li a i {
	color: #1de278;
}
.mydash .dropdown .dropdown-menu li a:hover {
	color: #1de278 !important;
}
@media screen and (min-width: 992px) {
/* shows the dropdown menu on hover */
.navbar .dropdown:hover .dropdown-menu, .navbar .dropdown .dropdown-menu:hover {
	display: block !important;
}
}
/* ===================================
	04. Slider
=================================== */
/* Slider 1 */
.slider .carousel-caption {
	top: 50%;
	bottom: auto;
	left: 0px;
	right: 0px;
	padding-top: 0px;
	padding-bottom: 0px;
	transform: translateY(-50%);
	z-index: 1;
	text-align: left;
}
.slider .carousel-item:before {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
	background-color: rgba(0,0,0,0.6);
}
.slider .carousel-item {
	background-position: center center;
	-webkit-background-size: cover;
	background-size: cover;
	position: relative;
	padding: 400px 0px;
}
.slider .carousel-item.slider-one {
	background-image: url("../img/slider/1.jpg"); /*edit image*/
}
.slider .carousel-item.slider-two {
	background-image: url("../img/slider/2.jpg"); /*edit image*/
}
.slider .carousel-item.slider-three {
	background-image: url("../img/slider/3.jpg"); /*edit image*/
}
.slider .carousel-caption .slider-caption-box p {
	position: relative;
	margin-bottom: 10px;
	font-size: 13px;
	color: #fff;
	font-style: italic;
	display: inline-block;
}
.slider .carousel-caption h2 {
	font-weight: 600;
	margin-bottom: 30px;
	font-size: 44px;
	color: #fff;
	line-height: 56px;
	text-transform: capitalize;
}
.slider .carousel-indicators button {
	width: 6px;
	height: 6px;
	border-radius: 100%;
	margin: 0px 10px;
	position: relative;
	opacity: 1;
}
.slider .carousel-indicators button.active:after {
	position: absolute;
	content: "";
	width: 18px;
	height: 18px;
	border: 1px solid #1de278;
	border-radius: 100%;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}
.slider .carousel-indicators button.active {
	background-color: #1de278;
}
/* Slider 2 */
.slider-2 .carousel-item:before {
	position: absolute;
	content: "";
	width: 70%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
	background-color: rgba(255,255,255,0.8);
	clip-path: polygon(0 0, 90% 0, 70% 100%, 0% 100%)
}
.slider-2 .carousel-caption h2 {
	color: #000;
}
.slider-2 .carousel-caption .slider-caption-box p {
	padding: 6px 20px;
	border-radius: 30px;
	background-color: rgba(0,0,0,0.8);
}
.slider-2 .carousel-item.slider-four {
	background-image: url("../img/slider/4.jpg"); /*edit image*/
}
.slider-2 .carousel-item.slider-five {
	background-image: url("../img/slider/5.jpg"); /*edit image*/
}
.slider-2 .carousel-item.slider-six {
	background-image: url("../img/slider/6.jpg"); /*edit image*/
}
.slider-2 .carousel-item {
	padding: 300px 0px;
	background-size: auto;
	background-position: center top;
}
/* banner 3 */
.banner-3 {
	background: url("../img/slider/banner-3.jpg") fixed no-repeat;
	-webkit-background-size: cover;
	background-size: cover;
	position: relative;
	background-position: center top;
	z-index: 0;
	padding-top: 225px;
	padding-bottom: 55px;
}
.banner-3:before {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: -1;
	background-color: rgba(0,0,0,0.6);
}
.banner-3 .banner-caption-box {
	z-index: 2;
	position: relative;
}
.banner-3 .banner-caption-box p {
	position: relative;
	margin-bottom: 10px;
	font-size: 13px;
	color: #fff;
	font-style: italic;
	display: inline-block;
}
.banner-3 .banner-caption-box h2 {
	font-weight: 600;
	margin-bottom: 30px;
	font-size: 44px;
	color: #fff;
	line-height: 56px;
	text-transform: capitalize;
}
/* banner 4 */
.banner-4 {
	background: url("../img/slider/banner-4.jpg") fixed no-repeat;
	-webkit-background-size: cover;
	background-size: cover;
	position: relative;
	background-position: center top;
	z-index: 0;
	padding-top: 225px;
	padding-bottom: 55px;
}
.banner-4:before {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: -1;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#1de278+0,00aee3+100&0.85+0,0.85+100 */
	background: -moz-linear-gradient(left, rgba(29,226,120,0.85) 0%, rgba(0,174,227,0.85) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(left, rgba(29,226,120,0.85) 0%, rgba(0,174,227,0.85) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to right, rgba(29,226,120,0.85) 0%, rgba(0,174,227,0.85) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d91de278', endColorstr='#d900aee3', GradientType=1 ); /* IE6-9 */
}
.banner-4 .banner-caption-box {
	z-index: 2;
	position: relative;
}
.banner-4 .banner-caption-box p {
	position: relative;
	margin-bottom: 10px;
	font-size: 13px;
	color: #fff;
	font-style: italic;
	display: inline-block;
}
.banner-4 .banner-caption-box h2 {
	font-weight: 600;
	margin-bottom: 30px;
	font-size: 44px;
	color: #fff;
	line-height: 56px;
	text-transform: capitalize;
}
/* banner 5 */
.banner-5 {
	background: url("../img/slider/banner-5.jpg") fixed no-repeat;
	-webkit-background-size: cover;
	background-size: cover;
	position: relative;
	background-position: center top;
	z-index: 0;
	padding-top: 100px;
	padding-bottom: 90px;
}
.banner-5:before {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: -1;
	background-color: rgba(0,0,0,0.7);
}
.banner-5 .banner-caption-box h2 {
	color: #fff;
}
.banner-5 .banner-caption-box p {
	color: #fff;
	margin-bottom: 0px;
}
/* search banner */
.search-banner input, .search-banner select {
	height: 40px;
	border: 0px;
	font-size: 12px;
}
.search-banner button {
	height: 40px;
	border: 0px;
	font-size: 12px;
	border-radius: 4px;
	background-color: #1de278;
	text-transform: uppercase;
	color: #000;
	font-weight: 600;
}
/* search icon box */
.search-icon-box a {
	background-color: rgba(255,255,255,1);
	border-radius: 6px;
	display: inline-block;
	padding: 20px 16px;
	border: 2px solid rgba(0,0,0,0.6);
	color: #000;
	text-align: center;
	font-size: 12px;
	font-weight: 600;
	margin-bottom: 10px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.search-icon-box a span {
	width: 100%;
	font-size: 20px;
	display: inline-block;
}
.search-icon-box a:hover {
	background-color: #1de278;
}
/* banner newsletter */
.banner-newsletter input {
	border: 0px;
	border-radius: 40px;
	height: 50px;
	padding: 15px;
	color: #000;
	font-size: 14px;
}
.banner-newsletter button {
	background-color: #333;
	color: #fff;
	height: 50px;
	font-size: 14px;
	font-weight: 600;
	border-radius: 40px;
	text-transform: uppercase;
	padding: 6px 20px;
}
.banner-newsletter button:hover {
	background-color: #000;
	color: #fff;
}
/* ===================================
	05. About area
=================================== */
.about-us-info h6 {
	color: #000;
	text-decoration: underline;
	font-size: 14px;
	font-weight: 600;
	line-height: 34px;
	text-transform: uppercase;
	margin-bottom: 4px;
}
.about-us-info h2 {
	font-size: 30px;
	font-weight: 600;
	margin-bottom: 20px;
}
.about-us-info .fa-quote-left {
	color: #297ee8;
	font-size: 60px;
	opacity: 0.3;
	margin-bottom: 15px;
}
.about-img img {
	border-radius: 8px;
}
/* about 3 */
.video-img {
	position: relative;
}
.video-img img {
	border-radius: 8px;
	border: 2px solid rgba(255,255,255,0.6);
}
/* ===================================
	06. Portfolio
=================================== */
#portfoliolist .portfolio {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-o-box-sizing: border-box;
	display: none;
}
.filterlink li {
	margin-right: 0px !important;
}
.filterlink li span {
	cursor: pointer;
	padding: 6px 14px 6px 14px;
	display: inline-block;
	position: relative;
	font-size: 12px;
	font-weight: 500;
	color: #666;
	border-radius: 4px;
	text-transform: uppercase;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.filterlink li span.active {
	color: #fff;
	background-color: #000;
}
.filterlink li span:hover {
	color: #1de278;
}
/* ===================================
	07. Footer
=================================== */
/* footer dark */
.footer-dark {
	background-color: rgba(0,0,0,1);
}
.footer-title {
	color: #fff;
	font-weight: 600;
	font-size: 22px;
	text-transform: uppercase;
	margin-bottom: 14px;
}
/* footer contact info */
.footer-contact-info li {
	color: #fff;
	margin-bottom: 8px;
	font-size: 15px;
}
.footer-contact-info li:last-child {
	margin-bottom: 0px;
}
.footer-contact-info li a {
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.footer-contact-info li a:hover {
	color: #1de278;
}
.footer-contact-info li i {
	color: #ee4155;
	text-align: center;
	margin-right: 6px;
	font-size: 12px;
	border-radius: 100%;
}
/* footer link */
.footer-link {
	text-align: center;
	padding-top: 15px;
	padding-bottom: 10px;
	border-top: 1px solid rgba(255,255,255,0.1);
}
.footer-link li {
	padding-bottom: 5px;
	margin-right: 20px !important;
}
.footer-link li:last-child {
	margin-right: 0px !important;
}
.footer-link li a {
	color: #fff;
	font-size: 12px;
	display: inline-block;
	text-transform: uppercase;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.footer-link li a:hover {
	color: #1de278;
}
/* footer newsletter */
.footer-newsletter input {
	border: 0px;
	border-radius: 4px;
	height: 50px;
	padding: 15px;
	color: #000;
	font-size: 14px;
}
.footer-newsletter button {
	background-color: #1de278;
	color: #fff;
	height: 50px;
	font-size: 14px;
	font-weight: 600;
	text-transform: uppercase;
	padding: 6px 20px;
}
/* footer copyright */
.footer-dark .footer-copyright {
	background-color: #0e0e0e;
}
.footer-copyright {
	text-align: center;
	padding: 20px 0px;
}
/* footer light */
.footer-light {
	background-color: #f7f7f7;
}
.footer-light .footer-copyright {
	background-color: #eeeeee;
	color: #333;
}
.footer-light .footer-title {
	color: #000;
}
.footer-light .footer-contact-info li a {
	color: #333;
}
.footer-light .footer-contact-info li {
	color: #000;
}
.footer-light .footer-link {
	border-top: 1px solid rgba(0,0,0,0.1);
}
.footer-light .footer-link li a {
	color: #333;
}
.footer-light .footer-contact-info li a:hover, .footer-light .footer-link li a:hover {
	color: #1de278;
}
/* ===================================
	08. Inner page header
=================================== */
.inner-page-header {
	background-color: #000;
	position: relative;
	z-index: 0;
	padding: 70px 0px;
}
.inner-page-header h1 {
	color: #fff;
	font-weight: 400;
	font-size: 40px;
}
.inner-page-header .breadcrumb {
	margin: auto;
	display: inline-block;
}
.inner-page-header .breadcrumb li {
	float: none;
	color: #fff;
	font-size: 14px;
	text-transform: capitalize;
	display: inline-block;
	padding-left: 0px;
}
.inner-page-header .breadcrumb-item + .breadcrumb-item:before {
	padding: 0 10px 0 10px;
	content: "-";
	font-size: 16px;
	color: rgba(255, 255, 255, 0.6);
	vertical-align: middle;
}
.inner-page-header .breadcrumb li a {
	color: #1de278;
	font-weight: 600;
}
/* ===================================
	09. Services
=================================== */
/* single service img */
.single-service-img img {
	border-radius: 8px;
}
/* single service text */
.single-service-text h2 {
	font-weight: 600;
	font-size: 30px;
}
.single-service-text h4 {
	font-weight: 600;
	font-size: 22px;
}
/* ===================================
	10. Sidebar services
=================================== */
.sidebar-services {
	background-color: #000;
	border-radius: 8px;
	position: relative;
	z-index: 0;
	padding: 16px 0px;
	overflow: hidden;
}
.sidebar-services li a {
	padding: 16px 16px 16px 16px;
	display: flex;
	width: 100%;
	border-left: 2px solid rgba(255,255,255,0);
	border-bottom: 1px solid rgba(255,255,255,0.2);
	color: #fff;
	font-size: 12px;
	text-transform: uppercase;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.sidebar-services li:last-child a {
	border-bottom: 0px;
}
.sidebar-services li a:hover, .sidebar-services li.active a {
	color: #1de278;
	border-left: 2px solid #1de278;
	background-color: rgba(255,255,255,0.1);
}
.sidebar-services li a i {
	margin-left: auto;
}
.sidebar-services li:last-child {
	margin-bottom: 0px;
}
/* ===================================
	11. Sidebar about
=================================== */
.sidebar-about {
	background-image: url(../img/about/sidebar-about.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	background-size: cover;
	position: relative;
	z-index: 0;
	padding: 20px;
	text-align: center;
	border-radius: 6px;
	overflow: hidden;
}
.sidebar-about:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	background-color: rgba(0,0,0,0.6);
	z-index: -1;
}
.sidebar-about p {
	color: #fff;
}
/* ===================================
	12. Sidebar download brouchure
=================================== */
.sidebar-download-brouchure {
	background-color: #1a1a1a;
	position: relative;
	z-index: 0;
	padding: 20px;
	text-align: center;
	border-radius: 6px;
	overflow: hidden;
}
.sidebar-download-brouchure h4 {
	color: #fff;
	font-size: 22px;
	font-weight: 400;
	margin-bottom: 15px;
}
/* ===================================
	13. Single portfolio
=================================== */
.single-portfolio-img {
	background-image: url(../img/portfolio/single-portfolio-bg.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	background-size: cover;
	position: relative;
	padding: 40px;
	border-radius: 8px;
	overflow: hidden;
}
.project-info {
	border-radius: 8px;
	overflow: hidden;
	padding: 20px 30px;
	background-color: rgba(0,0,0,0.8);
}
.project-info li {
	padding: 10px 20px 10px 0px;
	font-size: 14px;
	color: #fff;
	border-bottom: 1px solid rgba(255,255,255,0.1);
}
.project-info li:last-child {
	border-bottom: 0px;
}
.project-info li .title {
	display: inline-block;
	font-weight: 600;
	width: 100%;
	margin-bottom: 6px;
	color: #fff;
	position: relative;
}
.project-description h2 {
	font-weight: 600;
	font-size: 30px;
}
/* ===================================
	14. Login
=================================== */
.login-bg {
	background-image: url(../img/bg/login-bg.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	background-size: cover;
	position: relative;
	z-index: 0;
	height: 100vh;
}
.login-bg:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	background-color: rgba(0,0,0,0.6);
	z-index: -1;
}
.form-signin {
	background-color: #fff;
	box-shadow: 0 5px 13px rgba(62,69,78,0.2);
	border-radius: 8px;
	padding: 20px;
	text-align: center;
}
.form-signin label {
	font-size: 14px;
	color: #000;
}
.form-signin .form-check {
	margin-bottom: 0px;
}
.form-signin .form-check a {
	color: #000;
	font-weight: 600;
}
.form-signin .form-check-input {
	width: 16px;
	height: 16px;
}
.form-signin .form-check-label {
	padding-left: 6px;
}
.form-signin .forgot-pass a {
	font-size: 14px;
	color: #000;
	font-weight: 600;
}
.form-signin .no-account {
	font-size: 14px;
	border-bottom: 1px solid rgba(0,0,0,0.1);
	padding-bottom: 10px;
	margin-bottom: 10px;
}
.form-signin .no-account a {
	color: #000;
	font-weight: 600;
}
/* ===================================
	15. Error page 404
=================================== */
.error-page-bg {
	background-image: url(../img/bg/error-page-bg.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	background-size: cover;
	position: relative;
	z-index: 0;
	height: 100vh;
}
.error-page-bg:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	background-color: rgba(0,0,0,0.6);
	z-index: -1;
}
.error-page-item {
	padding: 30px;
	background-color: #f5f5f5;
	text-align: center;
	border-radius: 8px;
}
.error-page-item h2 {
	margin-bottom: 12px;
}
/* ===================================
	16. Coming soon page
=================================== */
.coming-soon-page-bg {
	background-image: url(../img/bg/coming-soon-page-bg.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	background-size: cover;
	position: relative;
	z-index: 0;
	height: 100vh;
}
.coming-soon-page-bg:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	background-color: rgba(0,0,0,0.4);
	z-index: -1;
}
.coming-soon-text {
	text-align: center;
}
.coming-soon-text h2, .coming-soon-text p {
	color: #fff;
}
.coming-soon-text ul li span {
	background-color: rgba(0,0,0,0.6);
	width: 100px;
	height: 100px;
	line-height: 100px;
	font-size: 20px;
	color: #fff;
	border-radius: 100%;
	margin-bottom: 10px;
	display: inline-block
}
.coming-soon-text ul li p {
	margin-bottom: 0px;
	text-transform: capitalize;
}
/* ===================================
	17. Contact Us
=================================== */
/* contact form */
.contact-form input, .contact-form textarea {
	padding: 12px 12px;
	font-size: 12px;
}
.help-block ul {
	padding: 12px;
	color: #842029;
	background-color: #f8d7da;
	border-color: #f5c2c7;
	border-radius: 6px;
}
/* contact img */
.contact-img img {
	border-radius: 8px;
}
/* contact info */
.contact-info li {
	font-size: 14px;
	color: #000;
	margin-bottom: 12px;
}
.contact-info li:last-child {
	margin-bottom: 0px;
}
.contact-info li span {
	font-weight: 600;
	width: 120px;
	display: inline-block;
}
/* map */
.map-box iframe {
	width: 100%;
	border: 0px;
	height: 400px;
	border-radius: 8px;
	overflow: hidden;
}
/* ===================================
	18. Privacy policy
=================================== */
.privacy-policy-single-content h3 {
	color: #000;
	font-size: 22px;
}
.privacy-policy-single-content p {
	margin-bottom: 0px;
}
/* ===================================
	19. Gallery
=================================== */
.gallery-box {
	position: relative;
	border-radius: 8px;
	overflow: hidden;
}
.gallery-box:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	opacity: 0;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#1de278+0,000000+100&0.7+0,0.7+100 */
	background: -moz-linear-gradient(top, rgba(29,226,120,0.7) 0%, rgba(0,0,0,0.7) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(29,226,120,0.7) 0%, rgba(0,0,0,0.7) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(29,226,120,0.7) 0%, rgba(0,0,0,0.7) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b31de278', endColorstr='#b3000000', GradientType=0 ); /* IE6-9 */
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.gallery-box .gallery-zoom {
	position: absolute;
	top: 50%;
	left: 0px;
	right: 0px;
	margin: auto;
	width: 40px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	background-color: #000;
	transform: translateY(-50%);
	color: #fff;
	border-radius: 100%;
	z-index: 2;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.gallery-box .gallery-box-img img {
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.gallery-box:hover .gallery-box-img img {
	filter: blur(8px);
	-webkit-filter: blur(8px);
}
.gallery-box:hover .gallery-zoom, .gallery-box:hover:after {
	opacity: 1;
}
/* request a quote form */
.request-a-quote-form {
	background-color: rgba(29,226,120,0.8);
	padding: 25px;
	border-radius: 8px;
}
.request-a-quote-form h2 {
	color: #000;
	margin-bottom: 20px;
	font-size: 28px;
}
.request-a-quote-form .form-label {
	color: #fff;
}
.request-a-quote-form input, .request-a-quote-form select {
	border: 0px;
	padding: 16px 12px;
	font-size: 12px;
}
.request-quote-img img {
	border-radius: 8px;
}
/* ===================================
	20. Partners
=================================== */
.partners-block {
	overflow: hidden;
}
.partners-block .row {
	margin-right: -0.07143rem;
	margin-bottom: -0.07143rem;
}
.partners-img {
	border-right: 1px solid #ccc;
	border-bottom: 1px solid #ccc;
	padding-top: 2.14286rem;
	padding-bottom: 2.14286rem;
	padding-left: 1.07143rem;
	padding-right: 1.07143rem;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.partners-img:hover {
	background-color: #f5f5f5;
}
/* ===================================
	21. Listing
=================================== */
/* side bar */
.filter-widget .accordion-item:first-of-type .accordion-button, .filter-widget .accordion-item:first-of-type, .filter-widget .accordion-item:last-of-type {
	border-radius: 0px;
}
.filter-widget .accordion-item {
	margin-bottom: 10px;
	border-radius: 6px;
	overflow: hidden;
	border-top: 1px solid rgba(0,0,0,.125);
}
.filter-widget .accordion-item:last-child {
	margin-bottom: 0px;
	border-radius: 6px;
	overflow: hidden;
}
.filter-widget .accordion-item:first-child {
	border-radius: 6px;
	overflow: hidden;
}
.filter-widget .accordion-item .accordion-button {
	font-size: 14px;
	font-weight: 500;
}
.filter-widget .accordion-item .accordion-button:after {
	width: 15px;
	height: 15px;
	background-size: 12px;
}
.filter-widget .accordion-item .accordion-button:not(.collapsed) {
	color: #000;
	background-color: #defbec;
	box-shadow: inset 0 -1px 0 rgba(0,0,0,.125);
}
.filter-widget .accordion-item .accordion-body .form-check {
	align-items: center;
	display: flex;
}
.filter-widget .accordion-item .accordion-body .form-check .form-check-label {
	font-size: 12px;
	color: #000;
	margin-left: 8px;
}
.filter-widget .accordion-item .accordion-body .form-check:last-child {
	margin-bottom: 0px;
}
.filter-widget .accordion-item .accordion-body .form-check .form-check-input[type="radio"], .filter-widget .accordion-item .accordion-body .form-check .form-check-input[type="checkbox"] {
	margin-top: 0px;
}
/* ===================================
	22. List view
=================================== */
/* List view */
.list-view {
	border: 1px solid #dbdbdb;
	overflow: hidden;
	border-radius: 6px;
	padding: 15px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.list-view:hover {
	border: 1px solid #1de278;
}
.list-view .img img {
	max-width: 100%;
}
.list-view h4 {
	line-height: 20px;
}
.list-view h4 a {
	font-weight: 700;
	margin-top: 0px;
	color: #333;
	font-size: 18px;
	display: inline-block;
	margin-bottom: 10px;
}
.list-view .booking-box {
	border-left: 1px solid #e6e6e6;
}
.list-view .booking-box .line-box {
	margin-bottom: 8px;
}
.clear-padding {
	padding: 0;
}
.list-view .cruise-line-box {
	min-height: 90px;
	padding: 12px 0px;
	border-bottom: 1px solid #e6e6e6;
}
.list-view strong {
	color: #1de278;
	margin-right: 10px;
}
.list-view .cruise-line-box i {
	margin-right: 0px;
}
.list-view .cruise-line-box h5 {
	margin-top: 0px;
	line-height: 1.6;
}
.list-view .price {
	min-height: 88px;
	padding: 12px 0px;
	background: #1de278;
	color: #ffffff;
}
.list-view .price h3 {
	margin-top: 0px;
	font-weight: bold;
	color: #fff;
	font-family: 'Montserrat', sans-serif;
}
.list-view .price h5 {
	color: #fff;
	font-size: 18px;
	padding-bottom: 6px;
}
.list-view p {
	font-size: 13px;
	line-height: 22px;
}
.list-view .price a {
	color: #ffffff;
	font-weight: bold;
	font-size: 13px;
}
.all-btn a {
	padding: 4px 4px;
	font-size: 10px;
}
.pagination-box .page-item .page-link {
	color: #1de278;
	font-size: 13px;
}
.pagination-box .page-item.active .page-link {
	z-index: 3;
	color: #fff;
	background-color: #1de278;
	border-color: #fff;
}
/* ===================================
	23. Detail page
=================================== */
/* detail page btn */
.detail-page-btn-main {
	border-bottom: 1px solid #dde6ef;
	background-color: #fff;
}
.detail-page-btn-main a {
	display: inline-block;
	color: #333;
	padding: 20px 32px;
	font-size: 13px;
	border-right: 1px dashed #dee2ea;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.detail-page-btn-main a:last-child {
	border-right: 0px;
}
.detail-page-btn-main a:hover {
	color: #1de278;
}
/* single box */
.single-box .card-header {
	background-color: #fff;
	color: #000;
	font-size: 16px;
	font-weight: 500;
}
.single-box .card-body p {
	color: #333;
	font-size: 14px;
}
/* location map */
.location-map iframe {
	width: 100%;
	height: 350px;
	border: 0px;
}
/* summary box */
.summary-box ul li h5 {
	font-size: 12px;
	color: #1de278;
}
.summary-box ul li p {
	margin-bottom: 0px;
	color: #000;
}
.summary-box ul li {
	margin-bottom: 10px;
}
/* destination place */
.destination-place {
	border-bottom: 1px solid rgba(0,0,0,0.1);
	padding-bottom: 10px;
	margin-bottom: 10px;
	color: #333;
}
/* best price */
.best-price {
	border-top: 1px solid rgba(0,0,0,0.1);
	padding-top: 10px;
	margin-top: 10px;
}
.side-price {
	color: #1de278;
	font-size: 24px;
	font-weight: 600;
}
.total-price {
	border-top: 1px solid rgba(0,0,0,0.1);
	padding-top: 10px;
	display: flex;
	align-items: center;
	margin-bottom: 0px !important;
}
.total-price h5 {
	color: #000 !important;
	font-size: 20px !important;
}
.total-price p {
	color: #98ce44 !important;
	font-weight: 600;
	font-size: 20px !important;
}
/* sidebar widget */
.sidebar-widget {
	background-color: #fff;
	border: 1px solid rgba(0,0,0,0.1);
	border-radius: 6px;
	overflow: hidden;
}
