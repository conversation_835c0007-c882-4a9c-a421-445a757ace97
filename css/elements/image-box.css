@charset "utf-8";
/* CSS Document */

/* Image box 1 */
.image-box1 {
	position: relative;
	border-radius: 8px;
	overflow: hidden;
}
.image-box1:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	opacity: 0;
	z-index: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,000000+50&0.8+0,0+50 */
	background: -moz-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cc000000', endColorstr='#00000000', GradientType=0 ); /* IE6-9 */
}
.image-box1 h4 {
	position: absolute;
	top: 30px;
	left: 0px;
	right: 0;
	font-size: 18px;
	font-weight: 500;
	margin: auto;
	text-align: center;
	z-index: 1;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box1 h4 a {
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box1 h4 a:hover {
	color: #1de278;
}
.image-box1 .other-link {
	position: absolute;
	bottom: 30px;
	left: 0px;
	right: 0;
	margin: auto;
	text-align: center;
	z-index: 1;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box1 .other-link a {
	width: 40px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	display: inline-block;
	background-color: #000;
	border-radius: 100%;
	font-size: 12px;
	font-weight: 500;
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box1 .other-link a:hover {
	background-color: #1de278;
}
.image-box1:hover:after, .image-box1:hover h4, .image-box1:hover .other-link {
	opacity: 1;
}
/* Image box 2 */
.image-box2 {
	background-color: #fff;
	position: relative;
	border: 2px solid rgba(0,0,0,0.1);
	padding: 25px;
	border-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box2 .number {
	font-size: 60px;
	font-weight: 700;
	color: #000;
	line-height: 52px;
	margin-bottom: 10px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	-webkit-text-fill-color: rgba(0,0,0,0);
	-ms-text-fill-color: rgba(0,0,0,0);
	-moz-text-fill-color: rgba(0,0,0,0);
	-o-text-fill-color: rgba(0,0,0,0);
	-webkit-text-stroke-width: 2px;
	-ms-text-stroke-width: 2px;
	-moz-text-stroke-width: 2px;
	-o-text-stroke-width: 2px;
	-webkit-text-stroke-color: #c4c4c4;
	-ms-text-stroke-color: #c4c4c4;
	-moz-text-stroke-color: #c4c4c4;
	-o-text-stroke-color: #c4c4c4;
}
.image-box2 .title a {
	font-size: 18px;
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box2 .img img {
	border-radius: 6px;
}
.image-box2:hover {
	border: 2px solid #1de278;
}
.image-box2 .title a:hover {
	color: #1de278;
}
.image-box2:hover .number {
	-webkit-text-stroke-color: #000;
	-ms-text-stroke-color: #000;
	-moz-text-stroke-color: #000;
	-o-text-stroke-color: #000;
}
/* Image box 3 */
.image-box3 {
	background-color: #fff;
	position: relative;
	border-radius: 6px 6px 0px 0px;
	text-align: center;
}
.image-box3 .title {
	border-left: 2px solid rgba(0,0,0,0.1);
	border-right: 2px solid rgba(0,0,0,0.1);
	border-bottom: 2px solid rgba(0,0,0,0.1);
	padding: 20px;
	border-radius: 0px 0px 6px 6px;
	line-height: 20px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box3:hover .title {
	border-color: #1de278;
}
.image-box3 .title a {
	font-size: 18px;
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box3 .img {
	position: relative;
}
.image-box3 .img img {
	border-radius: 6px 6px 0px 0px;
}
.image-box3 .img .des {
	position: absolute;
	width: 96%;
	height: 92%;
	top: 50%;
	left: 0;
	transform: translateY(-50%) scale(0);
	right: 0px;
	margin: auto;
	border-radius: 6px;
	padding: 12px;
	background-color: rgba(0,0,0,0.6);
	z-index: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box3 .img .des p {
	color: #fff;
}
.image-box3:hover .img .des {
	transform: translateY(-50%) scale(1);
}
.image-box3 .title a:hover {
	color: #1de278;
}
/* Image box 4 */
.image-box4 {
	background-color: #fff;
	position: relative;
	border-radius: 6px;
	text-align: center;
	overflow: hidden;
}
.image-box4 .link-image-box {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	z-index: 5;
}
.image-box4 .img {
	position: relative;
}
.image-box4 .img img {
	border-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box4:hover .img img {
	transform: scale(1.2);
}
.image-box4 .img .des {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	right: 0px;
	margin: auto;
	border-radius: 6px;
	padding: 12px;
	background-color: rgba(0,0,0,0.6);
	z-index: 0;
}
.image-box4 .img .des h4 {
	color: #fff;
	text-align: center;
	width: 100%;
	font-size: 32px;
	padding-bottom: 6px;
}
.image-box4 .img .des h6 {
	color: #fff;
	text-align: center;
	width: 100%;
	font-size: 14px;
	font-weight: 300;
}
.image-box4 .img .des .des-sub {
	margin: auto;
}
/* Image box 5 */
.image-box5 {
	background-color: #fff;
	position: relative;
	overflow: hidden;
	border-radius: 6px 6px 0px 0px;
}
.image-box5 .title {
	line-height: 20px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box5:hover .title {
	border-color: #1de278;
}
.image-box5 .title a {
	font-size: 18px;
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box5 .img {
	position: relative;
}
.image-box5 .img img {
	border-radius: 6px 6px 0px 0px;
}
.image-box5 .img .star {
	position: absolute;
	bottom: 15px;
	left: 15px;
	font-size: 12px;
	color: red;
	z-index: 4;
}
.image-box5 .img .location {
	position: absolute;
	top: 15px;
	left: 15px;
	color: #fff;
	font-size: 12px;
	z-index: 4;
}
.image-box5 .img .location i {
	color: #1de278;
}
.image-box5 .img:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	background-color: rgba(0,0,0,0.6);
}
.image-box5 .des {
	border-left: 2px solid rgba(0,0,0,0.1);
	border-right: 2px solid rgba(0,0,0,0.1);
	border-bottom: 2px solid rgba(0,0,0,0.1);
	padding: 20px;
	border-radius: 0px 0px 6px 6px;
	padding: 12px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.image-box5 .des .tag {
	font-size: 11px;
	padding: 4px 12px;
	border-radius: 6px;
	color: #000;
	display: inline-block;
	background-color: #1de278;
}
.image-box5 .des p {
	color: #000;
}
.image-box5 .title a:hover {
	color: #1de278;
}
.image-box5 .des .price {
	color: #1de278;
	font-size: 22px;
	font-weight: 600;
}
.image-box5:hover .des {
	border-color: #1de278;
}
