@charset "utf-8";
/* CSS Document */

/* Counter 1 */
.single-counter-box {
	position: relative;
	text-align: center;
	padding: 0px 0px 0px 0px;
	border-right: 1px solid rgba(0,0,0,0.1);
}
.counter-area .col:last-child .single-counter-box {
	border-right: 0px;
}
.single-counter-box .counter-info-text span {
	color: #000;
	font-weight: 600;
	font-size: 42px;
	line-height: 40px;
	margin-bottom: 10px;
	display: inline-block;
}
.single-counter-box h4 {
	color: #6c757d;
	font-weight: 400;
	font-size: 16px;
	white-space: nowrap;
}
/* Counter 2 */
.counter-bg {
	background-image: url(../../img/bg/counter-bg.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	background-size: cover;
	position: relative;
	z-index: 0;
}
.counter-bg:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	background-color: rgba(0,0,0,0.7);
	z-index: -1;
}
.single-counter-box-2 {
	border-right: 1px solid rgba(255,255,255,0.2);
}
.single-counter-box-2 .counter-info-text span {
	color: #fff;
}
.single-counter-box-2 h4 {
	color: #CCC;
}

/* Medium Devices, Desktops */
@media (max-width: 991px) {
.single-counter-box {
	border-right: 0px;
}
}

/* Small Devices, Tablets */
@media (max-width: 767px) {
}

/* Extra Small Devices, Phones */
@media (max-width: 575px) {
.single-counter-box .counter-info-text span {
	font-size: 30px;
	line-height: 32px;
}
.single-counter-box h4 {
	font-size: 11px;
}
}
