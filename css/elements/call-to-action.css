@charset "utf-8";
/* CSS Document */

/* Call to action 1 */
.call-to-action-area {
	background-image: url(../../img/bg/call-to-action-bg.jpg);
	background-size: cover;
	background-position: center top;
	position: relative;
	z-index: 0;
}
.call-to-action-area:after {
	position: absolute;
	content: "";
	background-color: rgba(0,0,0,0.8);
	width: 100%;
	height: 100%;
	left: 0px;
	top: 0px;
	z-index: -1;
}
.cta-content {
	text-align: center;
}
.cta-content h3 {
	font-size: 18px;
	color: #1de278;
	font-weight: 400;
	margin-bottom: 10px;
}
.call-to-action-area h2 {
	color: #fff;
	font-weight: 700;
	font-size: 40px;
	line-height: 44px;
	margin-bottom: 30px;
}
.call-to-action-area h2 a {
	color: #fff;
	text-decoration: underline;
}
/* Call to action 2 */
.call-to-action-area-2 {
	background-image: url(../../img/bg/call-to-action-bg-2.jpg);
	background-size: cover;
	background-position: center top;
	position: relative;
	z-index: 0;
}
.call-to-action-area-2:after {
	position: absolute;
	content: "";
	background-color: rgba(29,226,120,0.7);
	width: 100%;
	height: 100%;
	left: 0px;
	top: 0px;
	z-index: -1;
}
.cta-content-2 {
	text-align: center;
}
.cta-content-2 h3 {
	font-size: 18px;
	color: #000;
	font-weight: 400;
	margin-bottom: 10px;
}
.call-to-action-area-2 h2 {
	color: #fff;
	font-weight: 700;
	font-size: 40px;
	line-height: 44px;
	margin-bottom: 15px;
}
.call-to-action-area-2 p {
	color: #fff;
}
.call-to-action-area-2 h2 a {
	color: #fff;
	text-decoration: underline;
}
/* Call to action 3 */
.call-to-action-area-3 {
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#1de278+0,00aee3+100&0.85+0,0.85+100 */
	background: -moz-linear-gradient(left, rgba(29,226,120,0.85) 0%, rgba(0,174,227,0.85) 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(left, rgba(29,226,120,0.85) 0%, rgba(0,174,227,0.85) 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to right, rgba(29,226,120,0.85) 0%, rgba(0,174,227,0.85) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d91de278', endColorstr='#d900aee3', GradientType=1 ); /* IE6-9 */
	position: relative;
}
.call-to-action-area-3 h2 {
	color: #fff;
	font-weight: 700;
	font-size: 40px;
	line-height: 44px;
	margin-bottom: 10px;
}
.call-to-action-area-3 p {
	color: #fff;
}
