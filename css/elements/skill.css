@charset "utf-8";
/* CSS Document */

/* skill area 1 */
.skill-area-bg {
	background-image: url(../../img/bg/skill-area-bg.jpg);
	background-size: cover;
	background-position: center top;
	position: relative;
	z-index: 0;
}
.skill-area-bg:after {
	position: absolute;
	content: "";
	background-color: rgba(0,0,0,0.7);
	width: 100%;
	height: 100%;
	left: 0px;
	top: 0px;
	z-index: -1;
}
.skill-content {
	background-color: #fff;
	padding: 40px;
	border-radius: 8px;
}
.skill-content h2 {
	font-weight: 600;
	font-size: 26px;
}
/* Skill progress */
.skill-progress .progress-title-holder {
	position: relative;
	font-size: 14px;
	color: #333;
	text-transform: capitalize;
	padding-bottom: 6px;
}
.skill-progress .progress-wrapper {
	width: 100%;
	z-index: 10;
}
.skill-progress .progress-mark {
	position: absolute;
	bottom: 0;
	line-height: 38px;
	transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-webkit-transform: translateX(-50%);
}
.skill-progress .progress-outter {
	background-color: #1de278;
	height: 2px;
}
.skill-progress .progress-content {
	width: 0%;
	height: 2px;
	background-color: #1de278;
}
.skill-progress .percent {
	color: #333;
	font-size: 12px;
	position: relative;
}
.skill-progress .percent:before {
	background-color: #1de278;
	border-radius: 100%;
	bottom: -18px;
	content: "";
	height: 12px;
	position: absolute;
	right: 13px;
	width: 12px;
}
.skill-progress .skill-bar {
	margin-bottom: 40px;
}
.skill-progress .skill-bar:last-child {
	margin-bottom: 0px;
}
.skill-img img {
	border-radius: 8px;
}
