@charset "utf-8";
/* CSS Document */

/* icon box 1 */
.icon-box-1 {
	padding: 0px 30px 30px 30px;
	border: 1px solid rgba(0,0,0,0.1);
	border-radius: 6px;
	background-color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.icon-box-1 .icon {
	width: 60px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	position: relative;
	margin-bottom: 30px;
	font-size: 18px;
	border-radius: 0px 0px 6px 6px;
	color: #fff;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#0c0c0c+50,000000+50 */
	background: #0c0c0c; /* Old browsers */
	background: -moz-linear-gradient(-45deg, #0c0c0c 50%, #000000 50%); /* FF3.6-15 */
	background: -webkit-linear-gradient(-45deg, #0c0c0c 50%, #000000 50%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(135deg, #0c0c0c 50%, #000000 50%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0c0c0c', endColorstr='#000000', GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
}
.icon-box-1:hover {
	border: 1px solid rgba(0,0,0,0.3);
}
.icon-box-1 .icon:after {
	content: "";
	border-top: 12px solid #000;
	border-left: 12px solid transparent;
	position: absolute;
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
}
.icon-box-1 h3 {
	font-size: 20px;
	font-weight: 500;
}
.icon-box-1 h3 a {
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.icon-box-1:hover h3 a {
	color: #1de278;
}
.icon-box-1 p {
	margin-bottom: 0px;
}
/* icon box 2 */
.icon-box-2 .icon-box-2-icon {
	height: 60px;
	width: 60px;
	line-height: 65px;
	float: left;
	text-align: center;
	position: relative;
	-webkit-border-radius: 90%;
	-moz-border-radius: 90%;
	border-radius: 100%;
	background-color: #f5f5f5;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.icon-box-2 .icon-box-2-icon i {
	font-size: 20px;
	color: #000;
}
.icon-box-2 .content {
	margin-left: 80px;
}
.icon-box-2 .content h3 {
	color: #000;
	font-size: 18px;
	text-transform: capitalize;
	font-weight: 500;
	margin-bottom: 8px;
}
.icon-box-2 .content p {
	margin-bottom: 0px;
}
.icon-box-2:hover .icon-box-2-icon {
	background-color: #1de278;
}
/* icon box 3 */
.icon-box-3 {
	padding: 30px;
	border: 1px solid rgba(0,0,0,0.1);
	border-radius: 6px;
	position: relative;
	background-color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.icon-box-3 .icon {
	position: relative;
	margin-bottom: 20px;
}
.icon-box-3 .icon i {
	width: 60px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	position: relative;
	font-size: 16px;
	background-color: #000;
	border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.icon-box-3 .icon a {
	position: absolute;
	top: 50%;
	right: 0;
	transform: translateY(-50%);
}
.icon-box-3:hover .icon i {
	border-radius: 100%;
}
.icon-box-3:hover {
	border: 1px solid rgba(0,0,0,0.3);
}
.icon-box-3 h3 {
	font-size: 20px;
	font-weight: 500;
}
.icon-box-3 h3 a {
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.icon-box-3:hover h3 a {
	color: #1de278;
}
.icon-box-3 p {
	margin-bottom: 0px;
}
