@charset "utf-8";
/* CSS Document */

/* Team 1 */
.team-box1 {
	border-radius: 8px;
	overflow: hidden;
	position: relative;
}
.team-box1 .team-text {
	position: absolute;
	bottom: 0px;
	width: 100%;
	background-color: rgba(0,0,0,0.8);
	left: 0px;
	padding: 15px;
	z-index: 2;
}
.team-box1 .team-text h4 {
	color: #fff;
	font-weight: 600;
	font-size: 18px;
}
.team-box1 .team-text span {
	color: #fff;
	height: 0;
	opacity: 0;
	transform: scaleY(0);
	display: block;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.team-box1:hover .team-text span {
	height: 20px;
	opacity: 1;
	transform: scaleY(1);
}
.team-box1 .team-social {
	position: absolute;
	left: 15px;
	z-index: 2;
	top: 15px;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.team-box1 .team-social a {
	width: 34px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	background-color: #1de278;
	color: #fff;
	border-radius: 100%;
	display: inline-block;
}
.team-box1:hover .team-social {
	opacity: 1;
}
