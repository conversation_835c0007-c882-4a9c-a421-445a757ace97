@charset "utf-8";
/* CSS Document */

/* Blog item 1 */
.blog-item-1 {
	width: 100%;
	position: relative;
	overflow: hidden;
	border-radius: 8px;
}
.blog-item-1 .blog-img {
	position: relative;
	display: inline-block;
	width: 100%;
}
.blog-item-1 .blog-img:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,000000+50&0.8+0,0+50 */
	background: -moz-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cc000000', endColorstr='#00000000', GradientType=0 ); /* IE6-9 */
}
.blog-item-1 .blog-img img {
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-1:hover .blog-img img {
	filter: blur(4px);
	-webkit-filter: blur(4px);
}
.blog-item-1 .blog-content {
	background-color: #f5f5f5;
	padding: 20px;
	position: relative;
	border-radius: 0px 0px 0px 8px;
}
.blog-item-1 .meta {
	position: absolute;
	top: 20px;
	left: 20px;
	z-index: 2;
}
.blog-item-1 .meta a {
	color: #fff;
	text-decoration: none;
	display: inline-block;
	margin-bottom: 6px;
	font-size: 12px;
	margin-right: 6px;
}
.blog-item-1 .meta a:hover {
	color: #1de278;
}
.blog-item-1 .title {
	font-size: 20px;
	margin-bottom: 10px;
	line-height: 26px;
}
.blog-item-1 .title a {
	text-decoration: none;
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-1 .title a:hover {
	color: #1de278;
}
.blog-item-1 .blog-img a.readmore {
	position: absolute;
	left: 20px;
	bottom: 20px;
	z-index: 2;
}
.blog-item-1:hover .blog-img a.readmore {
	background-color: #1de278;
	color: #000;
}
.blog-item-1 p {
	margin-bottom: 0px;
}
/* Blog item 2 */
.blog-item-2 {
	width: 100%;
	position: relative;
}
.blog-item-2 .blog-img {
	position: relative;
	display: inline-block;
	width: 100%;
	overflow: hidden;
	border-radius: 8px;
}
.blog-item-2 .blog-img:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,000000+50&0.8+0,0+50 */
	background: -moz-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cc000000', endColorstr='#00000000', GradientType=0 ); /* IE6-9 */
}
.blog-item-2 .blog-img img {
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-2:hover .blog-img img {
	filter: blur(4px);
	-webkit-filter: blur(4px);
}
.blog-item-2 .blog-content {
	background-color: #fff;
	border: 1px solid #f5f5f5;
	padding: 20px;
	position: relative;
	overflow: hidden;
	border-radius: 8px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-2:hover .blog-content {
	border: 1px solid #000;
}
.blog-item-2 .meta {
	border-top: 1px solid rgba(0,0,0,0.1);
	padding-top: 14px;
	margin-top: 14px;
	display: flex;
}
.blog-item-2 .meta a {
	color: #000;
	text-decoration: none;
	display: inline-block;
	font-size: 12px;
	margin-right: 6px;
	flex: 1 1 auto;
	text-align: center;
}
.blog-item-2 .meta a strong {
	font-style: italic;
}
.blog-item-2 .meta a:hover {
	color: #1de278;
}
.blog-item-2 .title {
	font-size: 20px;
	margin-bottom: 10px;
	line-height: 26px;
}
.blog-item-2 .title a {
	text-decoration: none;
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-2 .title a:hover {
	color: #1de278;
}
.blog-item-2 .blog-img a {
	position: absolute;
	right: 20px;
	bottom: 20px;
	z-index: 2;
}
.blog-item-2:hover .blog-img a {
	background-color: #1de278;
	color: #000;
}
.blog-item-2 p {
	margin-bottom: 0px;
}
/* Blog item 3 */
.blog-item-3 {
	width: 100%;
	position: relative;
	border: 2px solid rgba(0,0,0,0.2);
	padding: 15px 15px 0px 15px;
	overflow: hidden;
	border-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-3:hover {
	border: 2px solid rgba(0,0,0,1);
}
.blog-item-3 .blog-img {
	position: relative;
	display: inline-block;
	width: 100%;
	overflow: hidden;
	border-radius: 6px;
}
.blog-item-3 .blog-img img {
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-3:hover .blog-img img {
	filter: blur(4px);
	-webkit-filter: blur(4px);
}
.blog-item-3 .blog-content {
	background-color: #fff;
	position: relative;
}
.blog-item-3 .meta {
	border-top: 1px solid rgba(0,0,0,0.1);
	padding-top: 14px;
	margin-top: 14px;
	display: flex;
}
.blog-item-3 .meta a {
	color: #000;
	text-decoration: none;
	display: inline-block;
	font-size: 11px;
	margin-right: 4px;
	flex: 1 1 auto;
	text-align: center;
}
.blog-item-3 .meta a strong {
	font-style: italic;
}
.blog-item-3 .meta a:hover {
	color: #1de278;
}
.blog-item-3 .title {
	font-size: 20px;
	margin-bottom: 10px;
	line-height: 26px;
}
.blog-item-3 .title a {
	text-decoration: none;
	color: #000;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.blog-item-3 .title a:hover {
	color: #1de278;
}
.blog-item-3 .blog-img a {
	position: absolute;
	right: 10px;
	bottom: 10px;
	z-index: 2;
}
.blog-item-3:hover .blog-img a {
	background-color: #1de278;
	color: #000;
}
.blog-item-3 p {
	margin-bottom: 0px;
}
/* sidebar title */
.sidebar-title {
	font-size: 20px;
	font-weight: 600;
	padding-left: 30px;
	position: relative;
}
.sidebar-title:after {
	position: absolute;
	content: "";
	width: 20px;
	height: 2px;
	top: 50%;
	transform: translateY(-50%);
	left: 0px;
	background-color: #1de278;
}
/* sidebar item */
.sidebar-item {
	background-color: #f5f5f5;
	padding: 20px;
	border-radius: 8px;
}
/* search form */
.search-form input {
	padding: 18px 16px;
	border: 0px;
	font-size: 12px;
}
.search-form button {
	background-color: #000;
	color: #fff;
}
.search-form button:hover {
	color: #fff;
	background-color: #1de278;
}
/* sidebar categories */
.sidebar-categories {
	background-color: #fff;
	padding: 0px 15px;
	border-radius: 8px;
}
.sidebar-categories li a {
	width: 100%;
	color: #000;
	font-size: 12px;
	display: flex;
	align-items: center;
	border-bottom: 1px dashed rgba(0,0,0,0.1);
	padding: 15px 0px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.sidebar-categories li:last-child a {
	border-bottom: 0px;
}
.sidebar-categories li a span {
	margin-left: auto;
	font-size: 10px;
	width: 30px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	background-color: #000;
	border-radius: 100%;
	color: #fff;
}
.sidebar-categories li a:hover {
	color: #1de278;
}
/* recent posts */
.recent-posts li a {
	width: 100%;
	color: #000;
	font-size: 12px;
	margin-bottom: 14px;
	display: flex;
	align-items: center;
	border-bottom: 1px solid rgba(0,0,0,0.1);
	padding: 0px 0px 14px 0px;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.recent-posts li:last-child a {
	border-bottom: 0px;
	padding-bottom: 0px;
	margin-bottom: 0px;
}
.recent-posts li .recent-post-img {
	margin-right: 12px;
	float: left;
}
.recent-posts li .recent-post-img img {
	border-radius: 8px;
	border: 2px solid rgba(0,0,0,0.2);
}
.recent-posts li a:hover {
	color: #1de278;
}
.recent-posts li a .date-info {
	width: 100%;
	display: inline-block;
	font-size: 10px;
	margin-bottom: 4px;
	color: #1de278;
}
/* tags list */
.sidebar-tags {
	display: inline-block;
}
.sidebar-tags li {
	float: left;
}
.sidebar-tags li a {
	background-color: #000;
	color: #fff;
	font-weight: 400;
	line-height: 100%;
	margin: 0 5px 5px 0;
	border-radius: 4px;
	display: inline-block;
	font-size: 12px;
	padding: 12px 12px;
	text-transform: capitalize;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.sidebar-tags li a:hover {
	background-color: #1de278;
	color: #fff;
}
/* blog post */
.blog-post-content h2 {
	font-weight: 600;
	font-size: 26px;
}
.blog-post-img {
	position: relative;
	overflow: hidden;
	border-radius: 8px;
}
.blog-post-img:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,000000+50&0.8+0,0+50 */
	background: -moz-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 50%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cc000000', endColorstr='#00000000', GradientType=0 ); /* IE6-9 */
}
.blog-post-img img {
	border-radius: 8px;
}
.blog-post-img-2 img {
	border-radius: 8px;
}
.blog-post-meta {
	position: absolute;
	top: 20px;
	left: 20px;
	z-index: 2;
}
.blog-post-meta a {
	color: #fff;
	text-decoration: none;
	display: inline-block;
	margin-bottom: 6px;
	font-size: 12px;
	margin-right: 6px;
}
.blog-post-meta a:hover {
	color: #1de278;
}
.releted-tags a {
	display: inline-block;
	background-color: #333;
	padding: 6px 16px;
	font-size: 12px;
	border-radius: 4px;
	margin-bottom: 6px;
	color: #fff;
	text-transform: capitalize;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.releted-tags a:hover {
	background-color: #1de278;
	color: #fff;
}
/* Comment box */
.box-comment {
	margin-bottom: 40px;
}
.box-comment:last-child {
	margin-bottom: 0px;
}
.box-comment .box-comment {
	padding-left: 30px;
	margin-top: 40px;
	border-left: 2px solid #1de278;
}
.box-comment .box-comment:last-child {
	margin-bottom: 0px;
}
.box-comment figure {
	margin: 0 20px 0 0;
	width: 100px;
}
.box-comment figure img {
	border-radius: 8px;
	border: 2px solid rgba(0,0,0,0.2);
}
.box-comment h6 {
	font-size: 18px;
	color: #333;
}
.box-comment h6 span {
	font-size: 11px;
	color: #6C6D74;
	display: flex;
	font-weight: 400;
}
.box-comment h6 span .current-year {
	margin-left: 8px;
}
.box-comment .review-text {
	width: 100%;
	position: relative;
}
.box-comment figure a {
	background-color: #333;
	color: #fff;
	border-radius: 4px;
	padding: 6px 10px;
	display: inline-block;
	font-size: 10px;
	margin-top: 8px;
	width: 100%;
	text-align: center;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.box-comment figure a:hover {
	background-color: #1de278;
}
.box-comment p {
	margin-bottom: 0px;
	margin-top: 8px;
	background-color: #f5f5f5;
	padding: 15px;
	border-radius: 8px;
}
/* post comment form */
.post-comment-form input, .post-comment-form textarea {
	padding: 12px 12px;
	font-size: 14px;
}

/* Medium Devices, Desktops */
@media (max-width: 1024px) {
.blog-item-2 .meta a {
	font-size: 10px;
	margin-right: 2px;
}
.blog-item-3 .meta a {
	margin-right: 2px;
	font-size: 9px;
}
}

/* Medium Devices, Desktops */
@media (max-width: 991px) {
.blog-item-2 .meta a {
	font-size: 10px;
}
.blog-item-3 .meta a {
	margin-right: 4px;
	font-size: 10px;
}
}

/* Small Devices, Tablets */
@media (max-width: 767px) {
}

/* Extra Small Devices, Phones */
@media (max-width: 575px) {
.blog-item-2 .meta a {
	margin-right: 1px;
}
.box-comment .review-item {
	display: inline-block !important;
}
.box-comment figure {
	margin-right: 0px;
	margin-bottom: 10px;
}
.box-comment .box-comment {
	padding-left: 10px;
}
}
